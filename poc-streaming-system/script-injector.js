/**
 * Script Injection System for POC Streaming
 *
 * Handles injection of JavaScript into target tabs via CDP
 * Uses both Runtime.evaluate and Page.addScriptToEvaluateOnNewDocument
 * for persistence across reloads and redirects
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class ScriptInjector {
  constructor(browserManager, signalingServer) {
    this.browserManager = browserManager;
    this.signalingServer = signalingServer;
    this.injectedScripts = new Map(); // tabId -> { scriptId, isInjected }
    this.persistentScripts = new Map(); // tabId -> scriptId for new document scripts
    this.controlTabInjected = false;
  }

  /**
   * Get the external streamer script for target tabs
   */
  async getTargetTabStreamerScript(signalingServerUrl, tabId) {
    const scriptPath = path.join(process.cwd(), "target-tab-streamer.js");
    console.log({ scriptPath });
    let scriptContent = await fs.promises.readFile(scriptPath, "utf8");

    // Replace placeholders
    scriptContent = scriptContent.replace(
      "${SIGNALING_SERVER_URL}",
      signalingServerUrl
    );
    scriptContent = scriptContent.replace("${TAB_ID}", tabId);

    return scriptContent;
  }

  /**
   * Get the client-side script that will be injected into target tabs (legacy)
   */
  getTargetTabScript(tabId, signalingServerUrl) {
    return `
(function() {
  'use strict';
  
  console.log('[POC-Streaming] Target tab script initializing...');
  
  // Prevent multiple injections
  if (window.pocStreamingInjected) {
    console.log('[POC-Streaming] Script already injected, skipping...');
    return;
  }
  window.pocStreamingInjected = true;
  
  class TargetTabStreaming {
    constructor() {
      this.tabId = '${tabId}';
      this.signalingServerUrl = '${signalingServerUrl}';
      this.websocket = null;
      this.isConnected = false;
      this.reconnectAttempts = 0;
      this.maxReconnectAttempts = 10;
      this.reconnectDelay = 1000;
      
      this.init();
    }
    
    async init() {
      console.log('[POC-Streaming] Initializing target tab streaming...');
      await this.connectToSignalingServer();
      this.setupPageListeners();
    }
    
    async connectToSignalingServer() {
      try {
        console.log('[POC-Streaming] Connecting to signaling server...');
        this.websocket = new WebSocket(this.signalingServerUrl);
        
        this.websocket.onopen = () => {
          console.log('[POC-Streaming] Connected to signaling server');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          
          // Register as target tab
          this.sendMessage({
            type: 'register-target-tab',
            tabId: this.tabId,
            url: window.location.href,
            title: document.title
          });
        };
        
        this.websocket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('[POC-Streaming] Failed to parse message:', error);
          }
        };
        
        this.websocket.onclose = () => {
          console.log('[POC-Streaming] Disconnected from signaling server');
          this.isConnected = false;
          this.scheduleReconnect();
        };
        
        this.websocket.onerror = (error) => {
          console.error('[POC-Streaming] WebSocket error:', error);
        };
        
      } catch (error) {
        console.error('[POC-Streaming] Failed to connect to signaling server:', error);
        this.scheduleReconnect();
      }
    }
    
    scheduleReconnect() {
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.error('[POC-Streaming] Max reconnection attempts reached');
        return;
      }
      
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      console.log(\`[POC-Streaming] Reconnecting in \${delay}ms (attempt \${this.reconnectAttempts})\`);
      setTimeout(() => {
        this.connectToSignalingServer();
      }, delay);
    }
    
    sendMessage(message) {
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        this.websocket.send(JSON.stringify(message));
      } else {
        console.warn('[POC-Streaming] Cannot send message - not connected');
      }
    }
    
    handleMessage(message) {
      console.log('[POC-Streaming] Received message:', message.type);
      
      switch (message.type) {
        case 'start-streaming':
          this.handleStartStreaming(message);
          break;
        case 'stop-streaming':
          this.handleStopStreaming(message);
          break;
        case 'webrtc-offer':
          this.handleWebRTCOffer(message);
          break;
        case 'webrtc-answer':
          this.handleWebRTCAnswer(message);
          break;
        case 'webrtc-ice-candidate':
          this.handleWebRTCIceCandidate(message);
          break;
        default:
          console.log('[POC-Streaming] Unknown message type:', message.type);
      }
    }
    
    handleStartStreaming(message) {
      console.log('[POC-Streaming] Starting streaming for tab:', this.tabId);
      
      // Notify that streaming is ready
      this.sendMessage({
        type: 'streaming-ready',
        tabId: this.tabId,
        streamId: message.streamId
      });
    }
    
    handleStopStreaming(message) {
      console.log('[POC-Streaming] Stopping streaming for tab:', this.tabId);
      
      // Clean up any streaming resources
      this.sendMessage({
        type: 'streaming-stopped',
        tabId: this.tabId,
        streamId: message.streamId
      });
    }
    
    handleWebRTCOffer(message) {
      console.log('[POC-Streaming] Received WebRTC offer');
      // WebRTC handling will be implemented in the WebRTC module
    }
    
    handleWebRTCAnswer(message) {
      console.log('[POC-Streaming] Received WebRTC answer');
      // WebRTC handling will be implemented in the WebRTC module
    }
    
    handleWebRTCIceCandidate(message) {
      console.log('[POC-Streaming] Received ICE candidate');
      // WebRTC handling will be implemented in the WebRTC module
    }
    
    setupPageListeners() {
      // Listen for page navigation events
      window.addEventListener('beforeunload', () => {
        console.log('[POC-Streaming] Page unloading...');
        this.sendMessage({
          type: 'tab-unloading',
          tabId: this.tabId
        });
      });
      
      // Listen for page load events
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
          this.sendMessage({
            type: 'tab-loaded',
            tabId: this.tabId,
            url: window.location.href,
            title: document.title
          });
        });
      } else {
        // Page already loaded
        this.sendMessage({
          type: 'tab-loaded',
          tabId: this.tabId,
          url: window.location.href,
          title: document.title
        });
      }
    }
  }
  
  // Initialize the target tab streaming
  window.pocStreaming = new TargetTabStreaming();
  
  console.log('[POC-Streaming] Target tab script initialized successfully');
})();
`;
  }

  /**
   * Get the control tab script
   */
  getControlTabScript(signalingServerUrl) {
    const controlTabScriptPath = path.join(__dirname, "control-tab-script.js");
    let script = fs.readFileSync(controlTabScriptPath, "utf8");

    // Replace the signaling server URL
    script = script.replace(
      "this.signalingServerUrl = 'ws://localhost:8080';",
      `this.signalingServerUrl = '${signalingServerUrl}';`
    );

    return script;
  }

  /**
   * Inject script into control tab
   */
  async injectControlTabScript(signalingServerUrl) {
    const controlTab = this.browserManager.controlTab;
    if (!controlTab) {
      throw new Error("Control tab not found");
    }

    if (this.controlTabInjected) {
      console.log("Control tab script already injected");
      return;
    }

    console.log("💉 Injecting control tab script...");

    const script = this.getControlTabScript(signalingServerUrl);

    try {
      // Inject into current page
      const result = await controlTab.cdp.Runtime.evaluate({
        expression: script,
        awaitPromise: false,
        returnByValue: false,
      });

      if (result.exceptionDetails) {
        console.error(
          "Control tab script injection error:",
          result.exceptionDetails
        );
        throw new Error(
          `Control tab script injection failed: ${result.exceptionDetails.text}`
        );
      }

      // Try to add script for new document loads (optional - may not be supported in all Chrome versions)
      try {
        await controlTab.cdp.Page.addScriptToEvaluateOnNewDocument({
          source: script,
        });
        console.log("✅ Persistent script added for control tab");
      } catch (persistentError) {
        console.log(
          "⚠️  Persistent script not supported, using immediate injection only"
        );
      }

      this.controlTabInjected = true;
      console.log("✅ Control tab script injected successfully");
    } catch (error) {
      console.error("❌ Failed to inject control tab script:", error);
      throw error;
    }
  }

  /**
   * Inject streamer script into a target tab
   */
  async injectScript(tabId, signalingServerUrl) {
    const targetTab = this.browserManager.getTargetTab(tabId);
    if (!targetTab) {
      throw new Error(`Target tab not found: ${tabId}`);
    }

    console.log(`💉 Injecting streamer script into tab: ${tabId}`);

    // Use the new external streamer script
    const script = await this.getTargetTabStreamerScript(
      signalingServerUrl,
      tabId
    );

    try {
      // Method 1: Inject into current page
      const result = await targetTab.cdp.Runtime.evaluate({
        expression: script,
        awaitPromise: false,
        returnByValue: false,
      });

      if (result.exceptionDetails) {
        console.error("Script injection error:", result.exceptionDetails);
        throw new Error(
          `Script injection failed: ${result.exceptionDetails.text}`
        );
      }

      // Method 2: Try to add script for new document loads (persistence)
      let persistentResult = null;
      try {
        persistentResult =
          await targetTab.cdp.Page.addScriptToEvaluateOnNewDocument({
            source: script,
          });
        console.log(`✅ Persistent script added for tab: ${tabId}`);
      } catch (persistentError) {
        console.log(
          `⚠️  Persistent script not supported for tab: ${tabId}, using immediate injection only ${persistentError}`
        );
      }

      // Store script IDs for cleanup
      this.injectedScripts.set(tabId, {
        isInjected: true,
        timestamp: Date.now(),
      });

      if (persistentResult && persistentResult.identifier) {
        this.persistentScripts.set(tabId, persistentResult.identifier);
      }

      targetTab.isInjected = true;

      console.log(`✅ Script injected successfully into tab: ${tabId}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to inject script into tab ${tabId}:`, error);
      throw error;
    }
  }

  /**
   * Remove injected script from a target tab
   */
  async removeScript(tabId) {
    const targetTab = this.browserManager.getTargetTab(tabId);
    if (!targetTab) {
      console.warn(`Target tab not found for script removal: ${tabId}`);
      return;
    }

    console.log(`🗑️ Removing script from tab: ${tabId}`);

    try {
      // Remove persistent script
      const persistentScriptId = this.persistentScripts.get(tabId);
      if (persistentScriptId) {
        await targetTab.cdp.Runtime.removeScriptToEvaluateOnNewDocument({
          identifier: persistentScriptId,
        });
        this.persistentScripts.delete(tabId);
      }

      // Clean up tracking
      this.injectedScripts.delete(tabId);
      targetTab.isInjected = false;

      console.log(`✅ Script removed from tab: ${tabId}`);
    } catch (error) {
      console.error(`❌ Failed to remove script from tab ${tabId}:`, error);
    }
  }

  /**
   * Check if script is injected in a tab
   */
  isScriptInjected(tabId) {
    return this.injectedScripts.has(tabId);
  }

  /**
   * Get all tabs with injected scripts
   */
  getInjectedTabs() {
    return Array.from(this.injectedScripts.keys());
  }

  /**
   * Cleanup all injected scripts
   */
  async cleanup() {
    console.log("🧹 Cleaning up script injector...");

    const tabIds = Array.from(this.injectedScripts.keys());
    for (const tabId of tabIds) {
      await this.removeScript(tabId);
    }

    console.log("✅ Script injector cleanup complete");
  }
}
