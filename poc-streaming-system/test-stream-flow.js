/**
 * Test Stream Flow
 * 
 * This script tests the complete streaming flow by simulating a web client
 * that connects and requests a stream from the target tab.
 */

import WebSocket from 'ws';

class StreamFlowTest {
  constructor() {
    this.ws = null;
    this.clientId = null;
    this.targetTabs = [];
    this.streamId = null;
  }

  async run() {
    try {
      console.log('🧪 Starting Stream Flow Test...');
      
      // Connect to signaling server
      await this.connectToSignalingServer();
      
      // Register as web client
      await this.registerAsWebClient();
      
      // Wait for target tabs
      await this.waitForTargetTabs();
      
      // Request stream
      await this.requestStream();
      
      // Wait for stream to be established
      await this.waitForStream();
      
      console.log('✅ Stream Flow Test completed successfully!');
      
    } catch (error) {
      console.error('❌ Stream Flow Test failed:', error);
      throw error;
    } finally {
      if (this.ws) {
        this.ws.close();
      }
    }
  }

  async connectToSignalingServer() {
    console.log('📡 Connecting to signaling server...');
    
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket('ws://localhost:8080');
      
      this.ws.on('open', () => {
        console.log('✅ Connected to signaling server');
        resolve();
      });
      
      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(message);
        } catch (error) {
          console.error('Failed to parse message:', error);
        }
      });
      
      this.ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        reject(error);
      });
      
      this.ws.on('close', () => {
        console.log('📡 Disconnected from signaling server');
      });
    });
  }

  async registerAsWebClient() {
    console.log('🌐 Registering as web client...');
    
    return new Promise((resolve) => {
      this.sendMessage({
        type: 'register-web-client',
        metadata: {
          userAgent: 'StreamFlowTest/1.0',
          timestamp: Date.now()
        }
      });
      
      // Wait a moment for registration to complete
      setTimeout(resolve, 1000);
    });
  }

  async waitForTargetTabs() {
    console.log('⏳ Waiting for target tabs...');
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Timeout waiting for target tabs'));
      }, 10000);
      
      const checkTabs = () => {
        if (this.targetTabs.length > 0) {
          clearTimeout(timeout);
          console.log(`✅ Found ${this.targetTabs.length} target tab(s)`);
          resolve();
        } else {
          setTimeout(checkTabs, 500);
        }
      };
      
      checkTabs();
    });
  }

  async requestStream() {
    if (this.targetTabs.length === 0) {
      throw new Error('No target tabs available');
    }
    
    const targetTab = this.targetTabs[0];
    console.log(`🎥 Requesting stream for target tab: ${targetTab.tabId}`);
    
    this.sendMessage({
      type: 'start-stream',
      tabId: targetTab.tabId
    });
  }

  async waitForStream() {
    console.log('⏳ Waiting for stream to be established...');
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Timeout waiting for stream'));
      }, 15000);
      
      const checkStream = () => {
        if (this.streamId) {
          clearTimeout(timeout);
          console.log(`✅ Stream established: ${this.streamId}`);
          resolve();
        } else {
          setTimeout(checkStream, 500);
        }
      };
      
      checkStream();
    });
  }

  handleMessage(message) {
    console.log(`📨 Received: ${message.type}`);
    
    switch (message.type) {
      case 'welcome':
        this.clientId = message.clientId;
        console.log(`✅ Registered with client ID: ${this.clientId}`);
        break;
        
      case 'available-streams':
        this.targetTabs = message.targetTabs;
        console.log(`📑 Received ${this.targetTabs.length} target tabs`);
        break;
        
      case 'target-tab-available':
        this.targetTabs.push(message);
        console.log(`📑 New target tab available: ${message.title}`);
        break;
        
      case 'stream-ready':
        console.log(`🎬 Stream ready: ${message.streamId}`);
        break;
        
      case 'webrtc-offer':
        console.log(`🤝 Received WebRTC offer for stream: ${message.streamId}`);
        this.streamId = message.streamId;
        // In a real implementation, we would handle the WebRTC offer here
        // For this test, we just acknowledge that we received it
        this.sendMessage({
          type: 'webrtc-answer',
          answer: { type: 'answer', sdp: 'mock-answer-sdp' },
          targetClientId: message.fromClientId,
          streamId: message.streamId
        });
        break;
        
      case 'stream-ended':
        console.log(`🛑 Stream ended: ${message.streamId}`);
        break;
        
      case 'error':
        console.error(`❌ Server error: ${message.message}`);
        break;
        
      default:
        console.log(`❓ Unknown message type: ${message.type}`);
    }
  }

  sendMessage(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('Cannot send message - not connected');
    }
  }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const test = new StreamFlowTest();
  
  test.run()
    .then(() => {
      console.log('🎉 Test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test failed:', error);
      process.exit(1);
    });
}

export { StreamFlowTest };
