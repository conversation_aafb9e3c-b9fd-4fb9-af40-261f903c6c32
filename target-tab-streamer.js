/**
 * Target Tab Streamer Script
 *
 * This script is injected into target tabs to automatically capture their content
 * and stream it via WebRTC. Uses preferCurrentTab: true for automatic capture.
 */

(() => {
  "use strict";

  // Prevent multiple injections
  if (window.POCTargetStreamer) {
    console.log("[POC-Streamer] Target streamer already initialized");
    return;
  }

  class TargetTabStreamer {
    constructor(signalingServerUrl) {
      this.signalingServerUrl = signalingServerUrl;
      this.ws = null;
      this.clientId = null;
      this.tabId = null;
      this.isConnected = false;
      this.captureStream = null;
      this.peerConnections = new Map(); // streamId -> RTCPeerConnection

      console.log("[POC-Streamer] Target tab streamer initializing...");
      this.init();
    }

    async init() {
      try {
        // Generate tab ID
        this.tabId = this.generateId();

        console.log("[POC-Streamer] Tab ID:", this.tabId);

        // Connect to signaling server
        await this.connectToSignalingServer();

        console.log(
          "[POC-Streamer] Target tab streamer initialized successfully"
        );
      } catch (error) {
        console.error(
          "[POC-Streamer] Failed to initialize target streamer:",
          error
        );
      }
    }

    async connectToSignalingServer() {
      return new Promise((resolve, reject) => {
        this.ws = new WebSocket(this.signalingServerUrl);

        this.ws.onopen = () => {
          console.log("[POC-Streamer] Connected to signaling server");
          this.isConnected = true;

          // Register as target tab
          this.sendMessage({
            type: "register-target-tab",
            tabId: this.tabId,
            url: window.location.href,
            title: document.title,
            metadata: {
              userAgent: navigator.userAgent,
              timestamp: Date.now(),
            },
          });

          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error("[POC-Streamer] Failed to parse message:", error);
          }
        };

        this.ws.onclose = () => {
          console.log("[POC-Streamer] Disconnected from signaling server");
          this.isConnected = false;

          // Attempt to reconnect after delay
          setTimeout(() => {
            if (!this.isConnected) {
              console.log("[POC-Streamer] Attempting to reconnect...");
              this.connectToSignalingServer().catch(console.error);
            }
          }, 5000);
        };

        this.ws.onerror = (error) => {
          console.error("[POC-Streamer] WebSocket error:", error);
          reject(error);
        };

        // Timeout for connection
        setTimeout(() => {
          if (!this.isConnected) {
            reject(new Error("Connection timeout"));
          }
        }, 10000);
      });
    }

    async handleMessage(message) {
      console.log("[POC-Streamer] Received message:", message.type);

      switch (message.type) {
        case "welcome":
          this.clientId = message.clientId;
          console.log(
            "[POC-Streamer] Registered with client ID:",
            this.clientId
          );
          break;

        case "start-streaming":
          await this.handleStartStream(message);
          break;

        case "webrtc-answer":
          await this.handleWebRTCAnswer(message);
          break;

        case "webrtc-ice-candidate":
          await this.handleICECandidate(message);
          break;

        default:
          console.log("[POC-Streamer] Unhandled message type:", message.type);
      }
    }

    async handleStartStream(message) {
      try {
        console.log("[POC-Streamer] Starting stream for:", message);

        // Capture current tab content automatically
        const stream = await this.captureCurrentTab();
        this.captureStream = stream;

        // Create peer connection for this stream
        const peerConnection = await this.createPeerConnection(message);

        // Add stream tracks to peer connection
        stream.getTracks().forEach((track) => {
          peerConnection.addTrack(track, stream);
        });

        // Create and send offer
        const offer = await peerConnection.createOffer();
        await peerConnection.setLocalDescription(offer);

        this.sendMessage({
          type: "webrtc-offer-from-target",
          offer: offer,
          streamId: message.streamId,
          targetClientId: message.requestedBy,
        });

        // Notify server that streaming is ready
        this.sendMessage({
          type: "streaming-ready",
          streamId: message.streamId,
          tabId: this.tabId,
        });

        console.log("[POC-Streamer] Stream started and offer sent");
      } catch (error) {
        console.error("[POC-Streamer] Failed to start stream:", error);

        // Send error back to signaling server
        this.sendMessage({
          type: "stream-error",
          streamId: message.streamId,
          error: error.message,
        });
      }
    }

    async captureCurrentTab() {
      try {
        console.log("[POC-Streamer] Capturing current tab content...");

        // Use getDisplayMedia with preferCurrentTab for automatic capture
        const stream = await navigator.mediaDevices.getDisplayMedia({
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            frameRate: { ideal: 30 },
          },
          audio: false, // Disable audio for simplicity
          preferCurrentTab: true, // Automatically capture this tab
        });

        console.log("[POC-Streamer] Tab content captured successfully");
        return stream;
      } catch (error) {
        console.error("[POC-Streamer] Failed to capture tab content:", error);

        // Fallback to a simple canvas stream
        return this.createFallbackStream();
      }
    }

    createFallbackStream() {
      console.log("[POC-Streamer] Creating fallback stream...");

      const canvas = document.createElement("canvas");
      canvas.width = 640;
      canvas.height = 480;
      const ctx = canvas.getContext("2d");

      // Create animated fallback content
      let frame = 0;
      const animate = () => {
        ctx.fillStyle = "#2196F3";
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = "white";
        ctx.font = "24px Arial";
        ctx.textAlign = "center";
        ctx.fillText(
          "Tab Content Capture",
          canvas.width / 2,
          canvas.height / 2 - 40
        );
        ctx.fillText(`${document.title}`, canvas.width / 2, canvas.height / 2);
        ctx.fillText(
          `Frame: ${frame++}`,
          canvas.width / 2,
          canvas.height / 2 + 40
        );

        requestAnimationFrame(animate);
      };
      animate();

      return canvas.captureStream(30);
    }

    async createPeerConnection(message) {
      const peerConnection = new RTCPeerConnection({
        iceServers: [{ urls: "stun:stun.l.google.com:19302" }],
      });

      // Store peer connection
      this.peerConnections.set(message.streamId, peerConnection);

      // Handle ICE candidates
      peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          this.sendMessage({
            type: "webrtc-ice-candidate-from-target",
            candidate: event.candidate,
            streamId: message.streamId,
            targetClientId: message.requestedBy,
          });
        }
      };

      // Handle connection state changes
      peerConnection.onconnectionstatechange = () => {
        console.log(
          "[POC-Streamer] Connection state:",
          peerConnection.connectionState
        );
      };

      return peerConnection;
    }

    async handleWebRTCAnswer(message) {
      const peerConnection = this.peerConnections.get(message.streamId);
      if (peerConnection) {
        await peerConnection.setRemoteDescription(message.answer);
        console.log("[POC-Streamer] WebRTC answer processed");
      }
    }

    async handleICECandidate(message) {
      const peerConnection = this.peerConnections.get(message.streamId);
      if (peerConnection) {
        await peerConnection.addIceCandidate(message.candidate);
      }
    }

    sendMessage(message) {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(message));
      }
    }

    generateId() {
      return Math.random().toString(36).substr(2, 9);
    }

    cleanup() {
      console.log("[POC-Streamer] Cleaning up target streamer...");

      // Stop capture stream
      if (this.captureStream) {
        this.captureStream.getTracks().forEach((track) => track.stop());
      }

      // Close peer connections
      for (const [streamId, pc] of this.peerConnections) {
        pc.close();
      }
      this.peerConnections.clear();

      // Close WebSocket
      if (this.ws) {
        this.ws.close();
      }
    }
  }

  // Initialize the target tab streamer
  const signalingServerUrl = "${SIGNALING_SERVER_URL}";
  window.POCTargetStreamer = new TargetTabStreamer(signalingServerUrl);

  // Cleanup on page unload
  window.addEventListener("beforeunload", () => {
    if (window.POCTargetStreamer) {
      window.POCTargetStreamer.cleanup();
    }
  });
})();
